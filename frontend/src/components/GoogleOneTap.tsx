import React, { useEffect, useRef } from 'react';
import { useMediaQuery, useTheme } from '@mui/material';

interface GoogleOneTapProps {
  onSuccess: (token: string) => void;
  onError?: (error: any) => void;
  disabled?: boolean;
  showDebugButton?: boolean;
}

declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void;
          prompt: (callback?: (notification: any) => void) => void;
          cancel: () => void;
          disableAutoSelect: () => void;
        };
      };
    };
  }
}

const GoogleOneTap: React.FC<GoogleOneTapProps> = ({
  onSuccess,
  onError,
  disabled = false,
  showDebugButton = false
}) => {
  const initialized = useRef(false);
  const clientIdRef = useRef<string | null>(null);
  const promptInProgress = useRef(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));



  useEffect(() => {
    const initializeOneTap = async () => {
      // Don't initialize if disabled or already initialized
      if (disabled || initialized.current) {
        return;
      }

      try {
        // Fetch Google Client ID from backend
        if (!clientIdRef.current) {
          const response = await fetch('/api/auth/google-client-id');
          if (!response.ok) {
            throw new Error('Failed to fetch Google Client ID');
          }
          const data = await response.json();
          clientIdRef.current = data.clientId;
        }

        // Wait for Google Identity Services to load
        if (!window.google?.accounts?.id) {
          // Retry with exponential backoff, max 10 seconds
          const retryCount = (initializeOneTap as any).retryCount || 0;
          if (retryCount < 50) { // 50 * 200ms = 10 seconds max
            (initializeOneTap as any).retryCount = retryCount + 1;
            setTimeout(initializeOneTap, 200);
          } else {
            console.warn('Google Identity Services failed to load after 10 seconds');
          }
          return;
        }

        // Initialize Google One Tap
        window.google.accounts.id.initialize({
          client_id: clientIdRef.current,
          callback: handleCredentialResponse,
          prompt_parent_id: 'google-one-tap-container', // Use a consistent ID
          auto_select: false,
          cancel_on_tap_outside: true,
          context: 'signin',
          ux_mode: 'popup',
          use_fedcm_for_prompt: true,  // Enable FedCM for future compatibility
          itp_support: true
        });

        // Show the One Tap prompt
        // With FedCM enabled, we call prompt without callback to avoid deprecated method warnings
        // Add protection against duplicate prompt calls
        if (!promptInProgress.current) {
          promptInProgress.current = true;
          try {
            window.google.accounts.id.prompt();
          } catch (error) {
            console.error('Error showing One Tap prompt:', error);
            promptInProgress.current = false;
          }
        }

        initialized.current = true;
      } catch (error) {
        console.error('Error initializing Google One Tap:', error);
        if (onError) {
          onError(error);
        }
      }
    };

    const handleCredentialResponse = async (response: any) => {
      try {
        if (!response.credential) {
          throw new Error('No credential received from Google One Tap');
        }

        // Send the ID token to our backend
        const authResponse = await fetch('/api/auth/one-tap-signin', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            idToken: response.credential
          })
        });

        if (!authResponse.ok) {
          const errorText = await authResponse.text();
          throw new Error(`Authentication failed: ${authResponse.status} - ${errorText}`);
        }

        const authData = await authResponse.json();

        // Store the JWT token and call success callback
        localStorage.setItem('jwt', authData.token);
        onSuccess(authData.token);

      } catch (error) {
        console.error('Error processing One Tap credential:', error);
        if (onError) {
          onError(error);
        }
      } finally {
        // Reset prompt progress flag after credential processing
        promptInProgress.current = false;
      }
    };

    // Initialize One Tap when component mounts
    initializeOneTap();

    // Cleanup function
    return () => {
      if (window.google?.accounts?.id && initialized.current) {
        try {
          window.google.accounts.id.cancel();
          promptInProgress.current = false;
        } catch (error) {
          // Silently handle cancellation errors
        }
      }
    };
  }, [disabled, onSuccess, onError, isMobile]); // Add isMobile to dependency array

  // Cancel One Tap when component becomes disabled
  useEffect(() => {
    if (disabled && window.google?.accounts?.id && initialized.current) {
      try {
        window.google.accounts.id.cancel();
      } catch (error) {
        // Silently handle cancellation errors
      }
    }
  }, [disabled]);

  const manualTrigger = () => {
    if (window.google?.accounts?.id && clientIdRef.current) {
      // With FedCM enabled, we call prompt without callback to avoid deprecated method warnings
      // Add protection against duplicate prompt calls
      if (!promptInProgress.current) {
        promptInProgress.current = true;
        try {
          window.google.accounts.id.prompt();
        } catch (error) {
          console.error('Error manually triggering One Tap prompt:', error);
          promptInProgress.current = false;
        }
      }
    }
  };

  return (
    <>
      <div
        id="google-one-tap-container"
        // Apply mobile styles only when isMobile is true
        style={isMobile ? {
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          zIndex: 1300,
          pointerEvents: 'none',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'flex-end',
          padding: '0 16px 16px 16px',
        } : {}} // On desktop, this div is just an empty, unstyled element
      >
        {/*
          The Google One Tap prompt will be injected here on both mobile and desktop.
          On desktop, it will ignore this and use its default top-right positioning.
          On mobile, it will use this container as its parent.
        */}
        <style>{isMobile ? `
          /* Mobile-specific styling for Google One Tap */
          #google-one-tap-container > div {
            pointer-events: auto !important;
            animation: slideUpFromBottom 0.3s ease-out;
            border-radius: 16px 16px 0 0 !important;
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15) !important;
            max-width: 400px;
            width: 100%;
          }

          @keyframes slideUpFromBottom {
            from { transform: translateY(100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
          }

          #google-one-tap-container iframe {
            border-radius: 16px 16px 0 0 !important;
          }
        ` : ''}</style>
      </div>

      {showDebugButton && (
        <div style={{ position: 'fixed', top: '10px', right: '10px', zIndex: 9999 }}>
          <button onClick={manualTrigger} style={{ padding: '10px', backgroundColor: '#4285f4', color: 'white', border: 'none', borderRadius: '4px' }}>
            Trigger One Tap
          </button>
        </div>
      )}
    </>
  );
};

export default GoogleOneTap;
